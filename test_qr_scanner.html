<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test QR Scanner</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Remix Icons -->
    <link href="https://cdn.jsdelivr.net/npm/remixicon@3.5.0/fonts/remixicon.css" rel="stylesheet">
    
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <style>
        body { padding: 20px; }
        .test-section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 8px; }
        .log { background: #f8f9fa; padding: 10px; border-radius: 4px; font-family: monospace; font-size: 12px; max-height: 300px; overflow-y: auto; }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .info { color: #17a2b8; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Test QR Scanner</h1>
        
        <div class="test-section">
            <h3>1. QR Scanner Functions Test</h3>
            <button class="btn btn-primary me-2" onclick="testPartyQR()">Test Party QR</button>
            <button class="btn btn-success me-2" onclick="testAssetQR()">Test Asset QR</button>
            <button class="btn btn-info me-2" onclick="testManualInput()">Test Manual Input</button>
            <button class="btn btn-secondary" onclick="clearLog()">Clear Log</button>
        </div>

        <div class="test-section">
            <h3>2. Debug Log</h3>
            <div id="debugLog" class="log">
                <div class="info">QR Scanner test ready...</div>
            </div>
        </div>
    </div>

    <!-- QR Scanner Modal (Copy from wizard.blade.php) -->
    <div class="modal fade" id="qrScannerModal" tabindex="-1" aria-labelledby="qrScannerModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="qrScannerModalLabel">
                        <i class="ri-qr-scan-line me-2"></i>Quét QR Code
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body text-center">
                    <div id="qr-scanner-container">
                        <video id="qr-video" width="100%" height="400" style="border: 2px solid #ddd; border-radius: 8px; display: none;"></video>
                        <canvas id="qr-canvas" style="display: none;"></canvas>
                        <div id="qr-loading" class="py-5">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Đang khởi động camera...</span>
                            </div>
                            <p class="mt-3 text-muted">Đang khởi động camera...</p>
                        </div>
                        <div id="qr-error" class="alert alert-danger" style="display: none;">
                            <i class="ri-error-warning-line me-2"></i>
                            <span id="qr-error-message">Không thể truy cập camera</span>
                        </div>
                        <div id="qr-instructions" class="mt-3" style="display: none;">
                            <p class="text-muted">
                                <i class="ri-information-line me-1"></i>
                                Đưa QR code vào khung hình để quét
                            </p>
                        </div>
                        <!-- Fallback for existing QR reader -->
                        <div id="qr-reader" style="width: 100%;"></div>
                        <div id="qr-reader-results"></div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Đóng</button>
                    <button type="button" class="btn btn-primary" id="qr-manual-input">Nhập thủ công</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logDiv = document.getElementById('debugLog');
            const logEntry = `<div class="${type}">[${timestamp}] ${message}</div>`;
            logDiv.innerHTML += logEntry;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        function clearLog() {
            document.getElementById('debugLog').innerHTML = '<div class="info">Debug log cleared...</div>';
        }

        function testPartyQR() {
            log('Testing Party QR Scanner...', 'info');
            
            // Mock party QR data
            const mockPartyData = {
                "type": "party",
                "full_name": "Nguyễn Văn A",
                "birth_year": 1990,
                "id_number": "123456789012",
                "id_type": "cccd",
                "current_address": "123 ABC Street, Hanoi",
                "phone": "0123456789",
                "email": "<EMAIL>"
            };

            // Test if openQRScanner function exists
            if (typeof window.openQRScanner === 'function') {
                log('✓ openQRScanner function exists', 'success');
                
                // Test opening QR scanner for party
                try {
                    window.openQRScanner('party');
                    log('✓ QR Scanner modal opened for party', 'success');
                } catch (error) {
                    log('❌ Error opening QR scanner: ' + error.message, 'error');
                }
            } else {
                log('❌ openQRScanner function not found', 'error');
            }

            // Test processPartyQRData if it exists
            if (typeof window.processPartyQRData === 'function') {
                log('✓ processPartyQRData function exists', 'success');
                try {
                    window.processPartyQRData(mockPartyData);
                    log('✓ Mock party data processed successfully', 'success');
                } catch (error) {
                    log('❌ Error processing party data: ' + error.message, 'error');
                }
            } else {
                log('⚠️ processPartyQRData function not found (may be in documents-wizard.js)', 'info');
            }
        }

        function testAssetQR() {
            log('Testing Asset QR Scanner...', 'info');
            
            // Mock asset QR data
            const mockAssetData = {
                "type": "asset",
                "asset_name": "Nhà số 123",
                "asset_code": "ASSET001",
                "field_values": {
                    "dia_chi": "123 ABC Street, Hanoi",
                    "dien_tich": "100",
                    "gia_tri": "1000000000"
                }
            };

            // Test if openQRScanner function exists
            if (typeof window.openQRScanner === 'function') {
                log('✓ openQRScanner function exists', 'success');
                
                // Test opening QR scanner for asset
                try {
                    window.openQRScanner('asset');
                    log('✓ QR Scanner modal opened for asset', 'success');
                } catch (error) {
                    log('❌ Error opening QR scanner: ' + error.message, 'error');
                }
            } else {
                log('❌ openQRScanner function not found', 'error');
            }

            // Test processAssetQRData if it exists
            if (typeof window.processAssetQRData === 'function') {
                log('✓ processAssetQRData function exists', 'success');
                try {
                    window.processAssetQRData(mockAssetData);
                    log('✓ Mock asset data processed successfully', 'success');
                } catch (error) {
                    log('❌ Error processing asset data: ' + error.message, 'error');
                }
            } else {
                log('⚠️ processAssetQRData function not found (may be in documents-wizard.js)', 'info');
            }
        }

        function testManualInput() {
            log('Testing Manual Input...', 'info');
            
            // Test if processManualQR function exists
            if (typeof window.processManualQR === 'function') {
                log('✓ processManualQR function exists', 'success');
                
                // Create a mock manual input
                const mockInput = document.createElement('input');
                mockInput.id = 'manualQRInput';
                mockInput.value = '{"type":"party","full_name":"Test User"}';
                document.body.appendChild(mockInput);
                
                try {
                    window.processManualQR();
                    log('✓ Manual QR processing test completed', 'success');
                } catch (error) {
                    log('❌ Error in manual QR processing: ' + error.message, 'error');
                }
                
                // Cleanup
                document.body.removeChild(mockInput);
            } else {
                log('❌ processManualQR function not found', 'error');
            }
        }

        // Test camera support
        function testCameraSupport() {
            if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
                log('✓ Camera API supported', 'success');
            } else {
                log('❌ Camera API not supported', 'error');
            }
        }

        // Initialize tests when document is ready
        $(document).ready(function() {
            log('QR Scanner test page loaded', 'info');
            testCameraSupport();
            
            // Test if QR scanner script is loaded
            setTimeout(function() {
                log('Checking QR scanner functions...', 'info');
                
                const functions = [
                    'openQRScanner',
                    'processManualQR'
                ];
                
                functions.forEach(function(funcName) {
                    if (typeof window[funcName] === 'function') {
                        log(`✓ ${funcName} function available`, 'success');
                    } else {
                        log(`❌ ${funcName} function not available`, 'error');
                    }
                });
            }, 1000);
        });
    </script>

    <!-- Include QR Scanner Script -->
    <script src="/resources/js/qr-scanner.js"></script>
</body>
</html>
