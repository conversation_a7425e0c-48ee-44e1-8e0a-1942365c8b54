<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test AJAX Step 3 - Template Parties</title>
    <meta name="csrf-token" content="test-token">
    <meta name="base-url" content="">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    
    <style>
        body { padding: 20px; }
        .test-section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 8px; }
        .log { background: #f8f9fa; padding: 10px; border-radius: 4px; font-family: monospace; font-size: 12px; max-height: 300px; overflow-y: auto; }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .info { color: #17a2b8; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Test AJAX Step 3 - Template Parties</h1>
        
        <div class="test-section">
            <h3>1. Test Configuration</h3>
            <div class="row">
                <div class="col-md-6">
                    <label for="baseUrl" class="form-label">Base URL:</label>
                    <input type="text" id="baseUrl" class="form-control" value="http://localhost:8000" placeholder="http://localhost:8000">
                </div>
                <div class="col-md-6">
                    <label for="templateId" class="form-label">Template ID:</label>
                    <input type="number" id="templateId" class="form-control" value="1" placeholder="1">
                </div>
            </div>
            <button class="btn btn-primary mt-3" onclick="testAjaxCall()">Test AJAX Call</button>
            <button class="btn btn-success mt-3" onclick="testMockResponse()">Test Mock Response</button>
            <button class="btn btn-secondary mt-3" onclick="clearLog()">Clear Log</button>
        </div>

        <div class="test-section">
            <h3>2. AJAX Response</h3>
            <div id="ajaxResponse" class="alert alert-info">
                Chưa có response. Nhấn "Test AJAX Call" để bắt đầu.
            </div>
        </div>

        <div class="test-section">
            <h3>3. Rendered Fields Preview</h3>
            <div id="fieldsPreview">
                <div class="text-muted">Fields sẽ được hiển thị ở đây sau khi AJAX call thành công.</div>
            </div>
        </div>

        <div class="test-section">
            <h3>4. Debug Log</h3>
            <div id="debugLog" class="log">
                <div class="info">Debug log sẽ hiển thị ở đây...</div>
            </div>
        </div>
    </div>

    <script>
        // Setup CSRF token
        $.ajaxSetup({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            }
        });

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logDiv = document.getElementById('debugLog');
            const logEntry = `<div class="${type}">[${timestamp}] ${message}</div>`;
            logDiv.innerHTML += logEntry;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        function clearLog() {
            document.getElementById('debugLog').innerHTML = '<div class="info">Debug log cleared...</div>';
            document.getElementById('ajaxResponse').innerHTML = 'Chưa có response.';
            document.getElementById('fieldsPreview').innerHTML = '<div class="text-muted">Fields sẽ được hiển thị ở đây sau khi AJAX call thành công.</div>';
        }

        function testAjaxCall() {
            const baseUrl = document.getElementById('baseUrl').value.trim();
            const templateId = document.getElementById('templateId').value.trim();
            
            if (!baseUrl || !templateId) {
                log('Vui lòng nhập Base URL và Template ID', 'error');
                return;
            }

            const url = `${baseUrl}/documents/ajax/template-parties`;
            
            log(`Starting AJAX test...`, 'info');
            log(`URL: ${url}`, 'info');
            log(`Template ID: ${templateId}`, 'info');

            // Show loading
            document.getElementById('ajaxResponse').innerHTML = `
                <div class="d-flex align-items-center">
                    <div class="spinner-border spinner-border-sm me-2" role="status"></div>
                    Đang gọi AJAX...
                </div>
            `;

            // AJAX call
            $.ajax({
                url: url,
                method: 'GET',
                data: { template_id: templateId },
                timeout: 10000,
                beforeSend: function(xhr) {
                    log('Sending AJAX request...', 'info');
                },
                success: function(response, textStatus, xhr) {
                    log('AJAX call successful!', 'success');
                    log(`Response status: ${xhr.status}`, 'success');
                    log(`Response data: ${JSON.stringify(response, null, 2)}`, 'info');
                    
                    // Show response
                    document.getElementById('ajaxResponse').innerHTML = `
                        <div class="alert alert-success">
                            <h5>✅ AJAX Call Successful!</h5>
                            <p><strong>Status:</strong> ${xhr.status}</p>
                            <p><strong>Response:</strong></p>
                            <pre>${JSON.stringify(response, null, 2)}</pre>
                        </div>
                    `;

                    // Validate and render fields
                    if (response && response.party_fields) {
                        log(`Found ${response.party_fields.length} party fields`, 'success');
                        renderFields(response.party_fields);
                    } else {
                        log('No party_fields found in response', 'error');
                        document.getElementById('fieldsPreview').innerHTML = `
                            <div class="alert alert-warning">
                                ⚠️ Response không chứa party_fields hoặc party_fields rỗng
                            </div>
                        `;
                    }
                },
                error: function(xhr, status, error) {
                    log(`AJAX call failed!`, 'error');
                    log(`Status: ${status}`, 'error');
                    log(`Error: ${error}`, 'error');
                    log(`HTTP Status: ${xhr.status}`, 'error');
                    log(`Response Text: ${xhr.responseText}`, 'error');
                    
                    let errorMessage = 'Có lỗi xảy ra khi gọi AJAX';
                    
                    if (xhr.status === 0) {
                        errorMessage = 'Không thể kết nối đến server. Kiểm tra Base URL và đảm bảo server đang chạy.';
                    } else if (xhr.status === 404) {
                        errorMessage = 'Endpoint không tồn tại (404). Kiểm tra routes.';
                    } else if (xhr.status === 500) {
                        errorMessage = 'Lỗi server (500). Kiểm tra logs server.';
                    } else if (xhr.status === 403) {
                        errorMessage = 'Không có quyền truy cập (403). Kiểm tra authentication.';
                    }

                    document.getElementById('ajaxResponse').innerHTML = `
                        <div class="alert alert-danger">
                            <h5>❌ AJAX Call Failed!</h5>
                            <p><strong>Error:</strong> ${errorMessage}</p>
                            <p><strong>Status:</strong> ${status} (${xhr.status})</p>
                            <p><strong>Details:</strong> ${error}</p>
                            ${xhr.responseText ? `<p><strong>Response:</strong></p><pre>${xhr.responseText}</pre>` : ''}
                        </div>
                    `;
                }
            });
        }

        function renderFields(fields) {
            if (!fields || fields.length === 0) {
                document.getElementById('fieldsPreview').innerHTML = `
                    <div class="alert alert-warning">
                        ⚠️ Không có fields để hiển thị
                    </div>
                `;
                return;
            }

            let html = '<div class="row">';
            
            fields.forEach(function(field, index) {
                const colClass = field.type === 'textarea' ? 'col-12' : 'col-md-6';
                const required = field.is_required ? 'required' : '';
                const requiredMark = field.is_required ? '<span class="text-danger">*</span>' : '';
                
                html += `<div class="${colClass} mb-3">`;
                html += `<div class="form-floating">`;
                
                if (field.type === 'textarea') {
                    html += `<textarea id="party_${field.name}" name="party_fields[${field.name}]" class="form-control" placeholder="${field.label}" ${required}></textarea>`;
                } else if (field.type === 'date') {
                    html += `<input type="date" id="party_${field.name}" name="party_fields[${field.name}]" class="form-control" ${required}>`;
                } else if (field.type === 'number') {
                    html += `<input type="number" id="party_${field.name}" name="party_fields[${field.name}]" class="form-control" placeholder="${field.label}" ${required}>`;
                } else {
                    html += `<input type="text" id="party_${field.name}" name="party_fields[${field.name}]" class="form-control" placeholder="${field.label}" ${required}>`;
                }
                
                html += `<label for="party_${field.name}">${field.label} ${requiredMark}</label>`;
                html += `</div>`;
                html += `</div>`;
            });
            
            html += '</div>';
            
            document.getElementById('fieldsPreview').innerHTML = `
                <div class="alert alert-success">
                    <h5>✅ Fields Rendered Successfully!</h5>
                    <p>Đã render ${fields.length} fields:</p>
                </div>
                ${html}
            `;

            log(`Successfully rendered ${fields.length} fields`, 'success');
        }

        function testMockResponse() {
            log('Testing with mock response...', 'info');

            // Mock response data (same structure as real API)
            const mockResponse = {
                "template": {
                    "id": 1,
                    "name": "Template mua bán nhà đất cơ bản"
                },
                "party_fields": [
                    {
                        "id": 1,
                        "name": "ho_ten_ben_a",
                        "label": "Họ và tên bên A",
                        "type": "text",
                        "section": "duong_su",
                        "is_required": 1,
                        "sort_order": 1,
                        "group_name": "Thông tin đương sự",
                        "custom_options": null
                    },
                    {
                        "id": 2,
                        "name": "cccd_ben_a",
                        "label": "CCCD/CMND bên A",
                        "type": "text",
                        "section": "duong_su",
                        "is_required": 0,
                        "sort_order": 2,
                        "group_name": "Thông tin đương sự",
                        "custom_options": null
                    },
                    {
                        "id": 3,
                        "name": "dia_chi_ben_a",
                        "label": "Địa chỉ bên A",
                        "type": "textarea",
                        "section": "duong_su",
                        "is_required": 0,
                        "sort_order": 3,
                        "group_name": "Thông tin đương sự",
                        "custom_options": null
                    },
                    {
                        "id": 4,
                        "name": "ho_ten_ben_b",
                        "label": "Họ và tên bên B",
                        "type": "text",
                        "section": "duong_su",
                        "is_required": 1,
                        "sort_order": 4,
                        "group_name": "Thông tin đương sự",
                        "custom_options": null
                    },
                    {
                        "id": 5,
                        "name": "cccd_ben_b",
                        "label": "CCCD/CMND bên B",
                        "type": "text",
                        "section": "duong_su",
                        "is_required": 0,
                        "sort_order": 5,
                        "group_name": "Thông tin đương sự",
                        "custom_options": null
                    },
                    {
                        "id": 6,
                        "name": "dia_chi_ben_b",
                        "label": "Địa chỉ bên B",
                        "type": "textarea",
                        "section": "duong_su",
                        "is_required": 0,
                        "sort_order": 6,
                        "group_name": "Thông tin đương sự",
                        "custom_options": null
                    }
                ]
            };

            log('Mock response created with 6 party fields', 'success');

            // Show mock response
            document.getElementById('ajaxResponse').innerHTML = `
                <div class="alert alert-info">
                    <h5>🧪 Mock Response Test</h5>
                    <p><strong>Status:</strong> 200 (Simulated)</p>
                    <p><strong>Response:</strong></p>
                    <pre>${JSON.stringify(mockResponse, null, 2)}</pre>
                </div>
            `;

            // Test rendering with mock data
            if (mockResponse && mockResponse.party_fields) {
                log(`Found ${mockResponse.party_fields.length} party fields in mock response`, 'success');
                renderFields(mockResponse.party_fields);
            } else {
                log('No party_fields found in mock response', 'error');
            }
        }

        // Auto-load on page ready
        $(document).ready(function() {
            log('Page loaded, ready for testing', 'info');
            log('Nhấn "Test AJAX Call" để test với server thật', 'info');
            log('Nhấn "Test Mock Response" để test với dữ liệu giả lập', 'info');
        });
    </script>
</body>
</html>
